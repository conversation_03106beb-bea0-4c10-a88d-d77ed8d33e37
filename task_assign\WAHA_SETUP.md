# 📱 WAHA (WhatsApp HTTP API) Setup Guide

This guide will help you set up WAHA to send WhatsApp notifications from the Task Assignment System.

## 🚀 Quick Setup with Docker (Recommended)

### 1. Install Docker
Make sure you have Docker installed on your system.

### 2. Run WAHA Container
```bash
docker run -it --rm -p 3000:3000/tcp devlikeapro/waha
```

### 3. Alternative: Run with Session Persistence
```bash
docker run -it --rm -p 3000:3000/tcp -v $(pwd)/.sessions:/app/.sessions devlikeapro/waha
```

## 📱 WhatsApp Setup

### 1. Start a Session
Open your browser and go to: `http://localhost:3000/dashboard`

### 2. Create Session
- Click "Add Session"
- Session name: `default` (must match the config in app.py)
- Click "Start Session"

### 3. Scan QR Code
- A QR code will appear
- Open WhatsApp on your phone
- Go to Settings → Linked Devices → Link a Device
- Scan the QR code

### 4. Verify Connection
- The session status should show "WORKING"
- You can test by sending a message through the dashboard

## 🔧 Configuration

The Flask app is configured to use:
```python
WAHA_CONFIG = {
    "base_url": "http://localhost:3000",
    "session": "default"
}
```

## 🔄 Auto-Restart Feature

The system includes automatic WAHA session restart functionality:

### ⚡ Automatic Recovery
- **Auto-restart every 1 minute**: System checks WAHA status and restarts if disconnected
- **Smart restart logic**: Creates session if missing, then restarts it
- **Background monitoring**: Runs automatically without user intervention

### 🎛️ Manual Controls
- **Dashboard button**: "Restart WhatsApp" button appears when disconnected
- **API endpoints**:
  - `GET /waha/status` - Check connection status
  - `POST /waha/restart` - Manual restart trigger
  - `POST /waha/create` - Create new session

### 📊 Status Monitoring
- **Real-time indicator**: Dashboard shows connection status
- **Auto-refresh**: Status updates every 10 seconds
- **Visual feedback**: Green (connected), Red (disconnected), Yellow (unknown)

## 📋 Testing WAHA Integration

### 1. Check WAHA Status
Visit: `http://127.0.0.1:5004/waha/status`

Should return:
```json
{
  "waha_connected": true,
  "waha_url": "http://localhost:3000",
  "session": "default"
}
```

### 2. Test WhatsApp Message
Run the test script:
```bash
python test_whatsapp.py
```

## 🔍 Troubleshooting

### WAHA Not Starting
- Check if port 3000 is available
- Try: `docker ps` to see running containers
- Check Docker logs: `docker logs <container_id>`

### QR Code Not Appearing
- Refresh the dashboard page
- Restart the WAHA container
- Clear browser cache

### Session Not Working
- Check session status in dashboard
- **Use auto-restart**: Wait 1-2 minutes for automatic restart
- **Manual restart**: Click "Restart WhatsApp" button on dashboard
- Restart session if status is "FAILED"
- Re-scan QR code if needed

### WhatsApp Messages Not Sending
- Verify session is "WORKING"
- Check phone number format (should be like: <EMAIL>)
- Check WAHA logs for errors

## 📱 Phone Number Format

The system automatically converts Malaysian phone numbers:
- Input: `+60123456789` → WhatsApp: `<EMAIL>`
- Input: `+601156229909` → WhatsApp: `<EMAIL>`

## 🔄 Production Setup

For production, consider:
1. **Persistent Storage**: Mount volumes for session data
2. **Environment Variables**: Use env vars for configuration
3. **Reverse Proxy**: Use nginx for SSL/domain setup
4. **Monitoring**: Set up health checks
5. **Backup**: Regular backup of session data

## 📚 WAHA Documentation

For more advanced features, visit:
- GitHub: https://github.com/devlikeapro/waha
- Documentation: https://waha.devlike.pro/

## 🆘 Support

If you encounter issues:
1. Check the WAHA dashboard at `http://localhost:3000/dashboard`
2. Check Flask app logs for error messages
3. Verify WhatsApp Web is working in your browser
4. Restart both WAHA and the Flask application
