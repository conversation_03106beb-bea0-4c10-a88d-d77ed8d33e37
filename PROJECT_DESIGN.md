# AI Agent PDF Processing Project - Design Document

## Project Overview
A PDF processing system that validates, extracts content using unstructured library, and integrates with n8n workflow for AI-powered chunking, embedding, and ChromaDB storage.

## Architecture Flow
```
HTML Upload Form → Python App (PDF Validation + Unstructured Extraction) → n8n Workflow (AI Chunking + Embedding) → Python App (ChromaDB Storage)
```

## Component Responsibilities

### 1. HTML Frontend (`pdf_upload/index.html`)
**Current State**: Basic form submitting directly to n8n webhook
**New Requirements**:
- Submit to Python app instead of n8n directly
- Display user-friendly success/error messages
- Handle JSON responses from Python app
- Show loading states during processing

### 2. Python App (`chroma_server/app.py`)
**Current State**: Only ChromaDB operations (add, query, print_all, delete_all)
**New Requirements**:
- **PDF Upload Endpoint** (`POST /upload-pdf`):
  - Validate file type (.pdf extension)
  - Use unstructured library to extract all content (text, tables, images)
  - Return extracted data in format suitable for n8n
  - Proper error handling and HTTP status codes
- **Enhanced ChromaDB Operations** (existing endpoints remain):
  - `POST /add` - Store embeddings (already exists)
  - `POST /query` - Query ChromaDB (already exists)
  - `POST /print_all` - Debug endpoint (already exists)
  - `POST /delete_all` - Cleanup endpoint (already exists)

### 3. n8n Workflow (handled by user)
**Changes Required**:
- Remove "Extract From File" node
- Webhook receives extracted data from Python app
- Continue with AI chunking and embedding
- Call Python app `/add` endpoint for storage

## Technical Specifications

### PDF Upload Endpoint Design
```
POST /upload-pdf
Content-Type: multipart/form-data
Body: pdfFile (file)

Success Response (200):
{
  "status": "success",
  "message": "PDF processed successfully",
  "extracted_data": {
    "text": "...",
    "tables": [...],
    "images": [...],
    // Format TBD based on unstructured output
  }
}

Error Responses:
400 - Invalid file type
{
  "status": "error",
  "message": "Only PDF files are allowed"
}

400 - No file uploaded
{
  "status": "error", 
  "message": "No file uploaded"
}

500 - Processing error
{
  "status": "error",
  "message": "Failed to process PDF: [error details]"
}
```

### HTML Form Updates
- Change form action to Python app endpoint
- Add JavaScript for:
  - AJAX form submission
  - Loading indicators
  - Success/error message display
  - File type validation (client-side)

### Dependencies to Add
- `unstructured` - PDF content extraction
- `python-multipart` or similar - File upload handling in Flask

## Error Handling Strategy

### Common Error Scenarios:
1. **No file uploaded** → 400 with clear message
2. **Wrong file type** → 400 with file type validation message  
3. **Corrupted PDF** → 500 with processing error message
4. **n8n workflow not running** → Need to handle this scenario
5. **Unstructured processing fails** → 500 with extraction error message

### User Experience:
- Clear loading states
- Descriptive error messages
- Success confirmation with next steps
- Option to try again on errors

## Development Tasks

### Phase 1: Python App Enhancement
1. Add PDF upload endpoint with file validation
2. Integrate unstructured library for content extraction
3. Implement proper error handling and logging
4. Test with various PDF types

### Phase 2: HTML Frontend Updates  
1. Update form to submit to Python app
2. Add JavaScript for AJAX handling
3. Implement user feedback (loading, success, error states)
4. Add client-side file validation

### Phase 3: Integration Testing
1. Test complete flow: HTML → Python → n8n → Python
2. Test error scenarios
3. Validate data format compatibility with n8n
4. Performance testing with large PDFs

## Data Format Considerations
- **Current**: n8n "Extract From File" returns simple text
- **New**: Unstructured returns structured data (text, tables, images)
- **Decision Pending**: Final format for n8n compatibility

## Security Considerations
- File size limits for PDF uploads
- File type validation (both client and server side)
- Input sanitization for extracted content
- Rate limiting for upload endpoint

## Monitoring & Debugging
- Enhanced logging for PDF processing steps
- Error tracking for failed extractions
- Performance metrics for processing time
- Debug endpoints for troubleshooting

---

## Implementation Status

### ✅ Completed
1. **PDF Upload Endpoint** (`POST /upload-pdf`):
   - File validation (PDF extension check)
   - **Enhanced unstructured integration** with table structure preservation
   - **Advanced table processing** to maintain column-data relationships
   - **n8n workflow trigger** after successful processing
   - Error handling with proper HTTP status codes
   - Temporary file management
   - Comprehensive logging for debugging

2. **n8n Workflow Integration**:
   - Automatic trigger of n8n webhook after PDF processing
   - Data format compatible with existing n8n workflow
   - Graceful handling when n8n is not available
   - Status reporting in API response

3. **HTML Form Updates**:
   - AJAX form submission to Python app
   - Loading states and user feedback
   - Client-side file validation
   - Success/error message display with workflow status
   - Form reset after successful upload

4. **Dependencies**:
   - Added requirements.txt with all necessary packages
   - Flask + Flask-CORS for web framework
   - Unstructured[pdf] for PDF processing
   - Requests for n8n webhook calls
   - Python-multipart for file uploads

5. **Comprehensive Unit Tests**:
   - 15+ test cases covering all scenarios
   - PDF validation tests
   - Upload endpoint tests
   - n8n workflow trigger tests
   - Error handling tests
   - Integration tests

### 🔄 Complete Flow Implementation
1. **HTML Form** → **Python App** (`/upload-pdf`)
2. **Python App** validates PDF → extracts with unstructured
3. **Python App** → **n8n Webhook** (triggers workflow)
4. **n8n Workflow** continues with AI chunking → embedding → ChromaDB storage
5. **User** gets feedback on both PDF processing and workflow trigger status

### � **Enhanced Table Processing Features**

**Problem Solved**: Table data was becoming meaningless chunks with lost column-data relationships.

**Solution Implemented**:
1. **High-resolution extraction** with `strategy="hi_res"` and `infer_table_structure=True`
2. **Table detection heuristics** to identify tables even when not classified as such
3. **Structure preservation** using key-value formatting and separators
4. **Table markers** `[TABLE]...[/TABLE]` for easy identification in chunks
5. **Fallback processing** for various table formats (pipe-separated, tab-separated, space-separated)

**Table Processing Examples**:
- Input: `Interface | 2 RJ-45 Ports | 1000Mbps`
- Output: `[TABLE]\nInterface: 2 RJ-45 Ports | 1000Mbps\n[/TABLE]`

**Detection Keywords**: Technical Specification, Interface, System, Network, Storage, Display

### �📋 Next Steps
1. Install dependencies: `pip install -r chroma_server/requirements.txt`
2. Start Python app: `python app.py`
3. Test complete flow with PDF upload (especially PDFs with tables)
4. Verify n8n workflow receives structured table data correctly
5. Check that AI chunking preserves table relationships
