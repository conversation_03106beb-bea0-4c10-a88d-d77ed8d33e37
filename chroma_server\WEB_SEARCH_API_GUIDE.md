# Web Search API Guide

## Overview

The Web Search API provides functionality to search the web using SearXNG and fetch content from the top results. This is perfect for AI agents that need to gather current information from the web.

## Endpoints

### 1. Web Search
**POST** `/web-search`

Searches for a query and fetches content from the top results.

#### Request Body
```json
{
    "query": "your search query"
}
```

#### Parameters
- `query` (string, required): The search query
- `num_results`: Fixed to 2 results (not configurable by users to manage token usage)

#### Response
```json
{
    "status": "success",
    "query": "your search query",
    "message": "Successfully processed 3 search results, 2 content fetches successful",
    "results": [
        {
            "search_title": "Title from search results",
            "fetched_title": "Title from actual webpage",
            "url": "https://example.com",
            "snippet": "Search result snippet",
            "content": "Full webpage content...",
            "content_length": 1234,
            "fetch_status": "success"
        },
        {
            "search_title": "Another result",
            "fetched_title": null,
            "url": "https://example2.com",
            "snippet": "Another snippet",
            "content": null,
            "content_length": 0,
            "fetch_status": "failed",
            "fetch_error": "Content too large: 113257 bytes (max: 50000)"
        }
    ],
    "total_results": 2,
    "successful_fetches": 1,
    "processing_time": 4.37
}
```

### 2. Health Check
**GET** `/web-search/health`

Checks if the web search service and SearXNG are working properly.

#### Response
```json
{
    "status": "success",
    "message": "Web search service is healthy",
    "service_status": "healthy",
    "searxng_url": "http://localhost:8888",
    "searxng_status": "connected",
    "searxng_message": "SearXNG is responding",
    "search_timeout": 30
}
```

## n8n Integration

### Basic HTTP Request Node Setup

1. **Method**: POST
2. **URL**: `http://localhost:5555/web-search`
3. **Headers**:
   ```
   Content-Type: application/json
   ```
4. **Body** (JSON):
   ```json
   {
       "query": "{{ $json.search_query }}"
   }
   ```

### Example n8n Workflow

```json
{
    "nodes": [
        {
            "name": "Web Search",
            "type": "n8n-nodes-base.httpRequest",
            "parameters": {
                "method": "POST",
                "url": "http://localhost:5555/web-search",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "query": "latest AI developments"
                }
            }
        }
    ]
}
```

### Processing Results in n8n

The response contains an array of results. You can:

1. **Loop through results** using a Split In Batches node
2. **Filter successful fetches** using an IF node:
   ```javascript
   {{ $json.fetch_status === "success" }}
   ```
3. **Extract content** using Set node:
   ```javascript
   {
       "title": "{{ $json.fetched_title || $json.search_title }}",
       "url": "{{ $json.url }}",
       "content": "{{ $json.content }}",
       "source": "web_search"
   }
   ```

## Error Handling

### Common Error Responses

#### SearXNG Not Available
```json
{
    "status": "error",
    "error_type": "search_error",
    "message": "Cannot connect to SearXNG at http://localhost:8888. Please ensure SearXNG Docker container is running"
}
```

#### Invalid Query
```json
{
    "status": "error",
    "error_type": "validation_error",
    "message": "Query must be a non-empty string"
}
```

#### Content Fetch Failures
Individual results may fail to fetch content, but the API will still return partial results:
```json
{
    "fetch_status": "failed",
    "fetch_error": "Content too large: 113257 bytes (max: 50000)"
}
```

## Configuration

### Current Settings
- **SearXNG URL**: `http://localhost:8888`
- **Search Timeout**: 30 seconds
- **Content Fetch Timeout**: 15 seconds
- **Max Content Length**: 50,000 bytes
- **Search Engines**: Google, Bing, DuckDuckGo

### Content Limitations
- Maximum content size: 50KB per webpage
- Content is cleaned (removes scripts, styles, navigation)
- Large pages are truncated with "... [Content truncated]"

## Testing

Run the test script to verify functionality:
```bash
python test_web_search.py
```

This will test:
- Health check endpoint
- Basic web search functionality
- Error handling
- Content fetching

## Troubleshooting

### SearXNG Issues
1. Check if Docker container is running:
   ```bash
   docker ps | grep searxng
   ```
2. Restart SearXNG if needed:
   ```bash
   docker restart searxng
   ```
3. Check SearXNG directly: http://localhost:8888

### Server Issues
1. Ensure the server is running on port 5555
2. Check server logs for detailed error messages
3. Verify BeautifulSoup4 is installed: `pip install beautifulsoup4`

### Performance Tips
- Use `num_results=1` for faster responses
- Some websites may be slow to fetch or blocked
- The API includes 1-second delays between content fetches to be respectful
