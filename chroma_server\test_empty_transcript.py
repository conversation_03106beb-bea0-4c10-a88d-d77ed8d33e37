#!/usr/bin/env python3
"""
Test script to verify empty transcript handling.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.routes.youtube_routes import _extract_youtube_transcript_if_needed
from src.flows.process_queue import QueuedProcess, ProcessType
from src.core.config import RAPIDAPI_KEY
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_empty_transcript_handling():
    """Test how the system handles a video with empty transcript."""
    
    print("🔍 Testing empty transcript handling...")
    print(f"Video URL: https://www.youtube.com/watch?v=9IZ_izMtBaU")
    
    # Create a mock process for the empty transcript video
    mock_process = QueuedProcess(
        session_id="empty-transcript-test",
        process_type=ProcessType.YOUTUBE,
        filename="Empty Transcript Test.yt",
        content="",  # Empty content for deferred extraction
        metadata={
            "source": "test",
            "video_url": "https://www.youtube.com/watch?v=9IZ_izMtBaU",
            "processing_method": "youtube_link_deferred",
            "deferred_youtube": True,
            "rapidapi_key": RAPIDAPI_KEY,
        },
        created_at=datetime.now()
    )
    
    print(f"Process metadata: {mock_process.metadata}")
    print(f"Initial content: '{mock_process.content}' (length: {len(mock_process.content)})")
    
    # Test the extraction function
    try:
        print("\n🔄 Calling _extract_youtube_transcript_if_needed...")
        result = _extract_youtube_transcript_if_needed(mock_process, logger)
        
        print(f"\n📊 Function returned: {result} (type: {type(result)})")
        print(f"Final content: '{mock_process.content}' (length: {len(mock_process.content)})")
        
        if result:
            print("✅ Extraction successful!")
            print(f"Content: {mock_process.content}")
            
            # Test if we can trigger n8n workflow with placeholder content
            print("\n🔄 Testing n8n workflow with placeholder content...")
            from src.flows.ingestion import trigger_n8n_workflow
            
            workflow_triggered, returned_session_id, total_chunks = trigger_n8n_workflow(
                mock_process.content, mock_process.filename, session_id=mock_process.session_id
            )
            print(f"N8N workflow triggered: {workflow_triggered}")
            print(f"Total chunks: {total_chunks}")
            print(f"Returned session ID: {returned_session_id}")
            
            if workflow_triggered:
                print("✅ Complete flow successful with placeholder content!")
                return True
            else:
                print("❌ N8N workflow failed with placeholder content")
                return False
        else:
            print("❌ Extraction failed!")
            return False
            
    except Exception as e:
        print(f"❌ Exception during extraction: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_empty_transcript_handling()
    print(f"\n🎯 Test result: {'PASSED' if success else 'FAILED'}")
