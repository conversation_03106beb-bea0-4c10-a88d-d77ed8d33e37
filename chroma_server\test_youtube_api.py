#!/usr/bin/env python3
"""
Test script to verify YouTube transcript extraction functionality.
"""

import http.client
import json
import sys

def test_rapidapi_connection():
    """Test RapidAPI YouTube transcript extraction."""
    
    # Test video ID (<PERSON> - Never Gonna Give You Up)
    video_id = "dQw4w9WgXcQ"
    
    try:
        conn = http.client.HTTPSConnection("youtube-transcriptor.p.rapidapi.com")
        
        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "youtube-transcriptor.p.rapidapi.com"
        }
        
        endpoint = f"/transcript?video_id={video_id}&lang=en"
        print(f"Testing endpoint: {endpoint}")
        
        conn.request("GET", endpoint, headers=headers)
        res = conn.getresponse()
        data = res.read()
        
        print(f"Response status: {res.status}")
        
        if res.status == 200:
            payload = json.loads(data.decode("utf-8"))
            print("✅ API call successful!")
            
            # Check response structure
            data_obj = payload[0] if isinstance(payload, list) and payload else payload
            
            print(f"Response type: {type(payload)}")
            print(f"Data keys: {list(data_obj.keys()) if isinstance(data_obj, dict) else 'Not a dict'}")
            
            # Try to extract transcript text
            transcript_text = None
            
            # Try direct strings
            for key in ("transcriptionAsText", "text"):
                if isinstance(data_obj.get(key), str) and data_obj[key].strip():
                    transcript_text = data_obj[key]
                    print(f"✅ Found transcript in '{key}' field")
                    break
            
            # Try arrays
            if transcript_text is None:
                parts = []
                for key in ("transcription", "transcript"):
                    if isinstance(data_obj.get(key), list) and data_obj[key]:
                        for seg in data_obj[key]:
                            seg_text = (seg.get("subtitle") or seg.get("text") or "").strip()
                            if seg_text:
                                parts.append(seg_text)
                if parts:
                    transcript_text = " ".join(parts)
                    print(f"✅ Found transcript in array format")
            
            if transcript_text:
                print(f"✅ Transcript extracted successfully!")
                print(f"Length: {len(transcript_text)} characters")
                print(f"Preview: {transcript_text[:200]}...")
                return True
            else:
                print("❌ No transcript text found in response")
                print(f"Raw response: {json.dumps(payload, indent=2)[:500]}...")
                return False
                
        else:
            error_msg = data.decode("utf-8")
            print(f"❌ API error: {res.status}")
            print(f"Error message: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def test_server_endpoint():
    """Test the server's YouTube link endpoint."""
    import requests
    
    try:
        response = requests.post(
            'http://localhost:5555/enqueue-youtube-link',
            json={
                'video_url': 'https://youtube.com/watch?v=dQw4w9WgXcQ',
                'video_title': 'Test Video',
                'source': 'test'
            },
            timeout=10
        )
        
        print(f"\nServer endpoint test:")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Server endpoint test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing YouTube transcript extraction...")
    print("=" * 50)
    
    # Test RapidAPI directly
    api_success = test_rapidapi_connection()
    
    print("\n" + "=" * 50)
    
    # Test server endpoint
    server_success = test_server_endpoint()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"RapidAPI direct test: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"Server endpoint test: {'✅ PASS' if server_success else '❌ FAIL'}")
    
    if api_success and server_success:
        print("\n🎉 All tests passed! YouTube upload should work correctly.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        sys.exit(1)
