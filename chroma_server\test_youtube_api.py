#!/usr/bin/env python3
"""
Test script to verify YouTube transcript extraction functionality.
"""

import http.client
import json
import sys

def test_rapidapi_connection():
    """Test RapidAPI YouTube transcript extraction."""
    
    # Test video ID (<PERSON> - Never Gonna Give You Up)
    video_id = "dQw4w9WgXcQ"
    
    try:
        conn = http.client.HTTPSConnection("youtube-transcriptor.p.rapidapi.com")
        
        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "youtube-transcriptor.p.rapidapi.com"
        }
        
        endpoint = f"/transcript?video_id={video_id}&lang=en"
        print(f"Testing endpoint: {endpoint}")
        
        conn.request("GET", endpoint, headers=headers)
        res = conn.getresponse()
        data = res.read()
        
        print(f"Response status: {res.status}")
        
        if res.status == 200:
            payload = json.loads(data.decode("utf-8"))
            print("✅ API call successful!")
            
            # Check response structure
            data_obj = payload[0] if isinstance(payload, list) and payload else payload
            
            print(f"Response type: {type(payload)}")
            print(f"Data keys: {list(data_obj.keys()) if isinstance(data_obj, dict) else 'Not a dict'}")
            
            # Try to extract transcript text
            transcript_text = None
            
            # Try direct strings
            for key in ("transcriptionAsText", "text"):
                if isinstance(data_obj.get(key), str) and data_obj[key].strip():
                    transcript_text = data_obj[key]
                    print(f"✅ Found transcript in '{key}' field")
                    break
            
            # Try arrays
            if transcript_text is None:
                parts = []
                for key in ("transcription", "transcript"):
                    if isinstance(data_obj.get(key), list) and data_obj[key]:
                        for seg in data_obj[key]:
                            seg_text = (seg.get("subtitle") or seg.get("text") or "").strip()
                            if seg_text:
                                parts.append(seg_text)
                if parts:
                    transcript_text = " ".join(parts)
                    print(f"✅ Found transcript in array format")
            
            if transcript_text:
                print(f"✅ Transcript extracted successfully!")
                print(f"Length: {len(transcript_text)} characters")
                print(f"Preview: {transcript_text[:200]}...")
                return True
            else:
                print("❌ No transcript text found in response")
                print(f"Raw response: {json.dumps(payload, indent=2)[:500]}...")
                return False
                
        else:
            error_msg = data.decode("utf-8")
            print(f"❌ API error: {res.status}")
            print(f"Error message: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def test_server_endpoint():
    """Test the server's YouTube link endpoint with dynamic video info."""
    import requests

    try:
        # First get video info from RapidAPI
        video_id = "dQw4w9WgXcQ"
        conn = http.client.HTTPSConnection("youtube-transcriptor.p.rapidapi.com")
        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "youtube-transcriptor.p.rapidapi.com"
        }
        endpoint = f"/transcript?video_id={video_id}&lang=en"
        conn.request("GET", endpoint, headers=headers)
        res = conn.getresponse()
        data = res.read()

        video_title = "Test Video"  # fallback
        if res.status == 200:
            payload = json.loads(data.decode("utf-8"))
            data_obj = payload[0] if isinstance(payload, list) and payload else payload
            video_title = data_obj.get('title', 'Test Video')
            print(f"📹 Video info: {video_title}")

        # Now test the server endpoint with dynamic title
        response = requests.post(
            'http://localhost:5555/enqueue-youtube-link',
            json={
                'video_url': f'https://youtube.com/watch?v={video_id}',
                'video_title': video_title,
                'source': 'test'
            },
            timeout=10
        )

        print(f"\nServer endpoint test:")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")

        return response.status_code == 200

    except Exception as e:
        print(f"❌ Server endpoint test failed: {e}")
        return False

def test_custom_video(video_url):
    """Test YouTube transcript extraction with a custom video URL."""
    import requests
    from urllib.parse import urlparse, parse_qs

    try:
        # Extract video ID from URL
        parsed = urlparse(video_url)
        video_id = None

        if parsed.hostname and "youtu.be" in parsed.hostname:
            video_id = parsed.path.lstrip("/")
        elif parsed.hostname and "youtube.com" in parsed.hostname:
            video_id = parse_qs(parsed.query).get("v", [None])[0]

        if not video_id:
            print(f"❌ Could not extract video ID from URL: {video_url}")
            return False

        print(f"🔍 Testing video ID: {video_id}")
        print(f"📺 URL: {video_url}")

        # Get video info from RapidAPI
        conn = http.client.HTTPSConnection("youtube-transcriptor.p.rapidapi.com")
        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "youtube-transcriptor.p.rapidapi.com"
        }
        endpoint = f"/transcript?video_id={video_id}&lang=en"
        conn.request("GET", endpoint, headers=headers)
        res = conn.getresponse()
        data = res.read()

        print(f"API Response status: {res.status}")

        if res.status == 200:
            payload = json.loads(data.decode("utf-8"))
            data_obj = payload[0] if isinstance(payload, list) and payload else payload

            # Extract video information
            title = data_obj.get('title', 'Unknown Title')
            duration = data_obj.get('lengthInSeconds', 'Unknown')
            description = data_obj.get('description', '')[:100] + '...' if data_obj.get('description') else 'No description'

            print(f"📹 Title: {title}")
            print(f"⏱️  Duration: {duration} seconds" if duration != 'Unknown' else f"⏱️  Duration: {duration}")
            print(f"📝 Description: {description}")

            # Check transcript
            transcript = data_obj.get('transcriptionAsText', '').strip()
            if transcript:
                print(f"✅ Transcript available: {len(transcript)} characters")
                print(f"📄 Preview: {transcript[:150]}...")
            else:
                print("⚠️  No transcript available or transcript is empty")

            # Test server endpoint
            print(f"\n🚀 Testing server endpoint...")
            response = requests.post(
                'http://localhost:5555/enqueue-youtube-link',
                json={
                    'video_url': video_url,
                    'video_title': title,
                    'source': 'test_custom'
                },
                timeout=15
            )

            print(f"Server response status: {response.status_code}")
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Successfully queued: {response_data.get('data', {}).get('session_id', 'Unknown session')}")
                return True
            else:
                print(f"❌ Server error: {response.text}")
                return False

        else:
            error_msg = data.decode("utf-8")
            print(f"❌ API error: {res.status} - {error_msg}")
            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    import sys

    # Check if a custom URL was provided as command line argument
    if len(sys.argv) > 1:
        custom_url = sys.argv[1]
        print("🔍 Testing custom YouTube video...")
        print("=" * 60)
        success = test_custom_video(custom_url)
        print("\n" + "=" * 60)
        if success:
            print("🎉 Custom video test passed!")
        else:
            print("❌ Custom video test failed!")
        sys.exit(0 if success else 1)

    print("🔍 Testing YouTube transcript extraction...")
    print("=" * 50)

    # Test RapidAPI directly
    api_success = test_rapidapi_connection()

    print("\n" + "=" * 50)

    # Test server endpoint
    server_success = test_server_endpoint()

    print("\n" + "=" * 50)

    # Test the custom video you provided
    print("🔍 Testing custom video: https://www.youtube.com/watch?v=yIaE9lONIVI")
    custom_success = test_custom_video("https://www.youtube.com/watch?v=yIaE9lONIVI")

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"RapidAPI direct test: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"Server endpoint test: {'✅ PASS' if server_success else '❌ FAIL'}")
    print(f"Custom video test: {'✅ PASS' if custom_success else '❌ FAIL'}")

    if api_success and server_success and custom_success:
        print("\n🎉 All tests passed! YouTube upload should work correctly.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        print("\n💡 You can also test a specific video by running:")
        print("   python test_youtube_api.py 'https://www.youtube.com/watch?v=VIDEO_ID'")
        sys.exit(1)
