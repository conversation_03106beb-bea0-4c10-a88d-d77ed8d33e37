{% extends "base.html" %}

{% block title %}Dashboard - Task Assign System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1><i class="fas fa-tachometer-alt"></i> Task Dashboard</h1>
        <div id="waha-status" class="mt-2">
            <span class="badge bg-secondary">
                <i class="fab fa-whatsapp"></i> <span class="d-none d-sm-inline">WhatsApp </span>Checking...
            </span>
            <button id="restart-waha-btn" class="btn btn-sm btn-outline-warning ms-2" onclick="restartWaha()" style="display: none;">
                <i class="fas fa-redo"></i> <span class="d-none d-sm-inline">Restart </span>WhatsApp
            </button>
        </div>
    </div>
    <a href="{{ url_for('create_task') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> <span class="d-none d-sm-inline">Create New </span>Task
    </a>
</div>

{% if tasks %}
<div class="table-responsive">
    <table class="table table-striped table-hover" style="min-width: 1200px;">
        <thead class="table-dark">
            <tr>
                <th style="width: 25%;">Task Description</th>
                <th style="width: 12%;">Assigned Employees</th>
                <th style="width: 15%;">Email</th>
                <th style="width: 12%;">Phone</th>
                <th style="width: 8%;">Created Date</th>
                <th style="width: 8%;">Deadline</th>
                <th style="width: 8%;">Status</th>
                <th style="width: 12%;">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for task in tasks %}
            <tr class="{{ 'table-success' if task.completed else '' }}" data-task-id="{{ task.id }}">
                <td>
                    <div class="fw-bold">{{ task.description[:100] }}{% if task.description|length > 100 %}...{% endif %}</div>
                </td>
                <td>
                    {% for employee in task.employees %}
                        <span class="badge bg-secondary me-1">{{ employee.name }}</span>
                        {% if not loop.last %}<br>{% endif %}
                    {% endfor %}
                </td>
                <td>
                    {% for employee in task.employees %}
                        <div>{{ employee.email }}</div>
                    {% endfor %}
                </td>
                <td>
                    {% for employee in task.employees %}
                        <div>{{ employee.formatted_phone }}</div>
                    {% endfor %}
                </td>
                <td>
                    <div>
                        {{ task.created_date.strftime('%d/%m/%Y') if task.created_date else task.created_at.strftime('%d/%m/%Y') }}
                    </div>
                </td>
                <td>
                    <div>
                        {{ task.deadline.strftime('%d/%m/%Y') }}
                    </div>
                </td>
                <td class="task-status">
                    {% if task.completed %}
                        <span class="badge bg-success"><i class="fas fa-check"></i> Completed</span>
                    {% else %}
                        <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                    {% endif %}
                </td>
                <td class="action-buttons">
                    <a href="{{ url_for('edit_task', id=task.id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    {% if not task.completed %}
                        <form method="POST" action="{{ url_for('complete_task', id=task.id) }}" class="d-inline">
                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Mark this task as completed?')">
                                <i class="fas fa-check"></i> Complete
                            </button>
                        </form>
                    {% endif %}
                    <form method="POST" action="{{ url_for('delete_task', id=task.id) }}" class="d-inline">
                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this task?')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center py-5">
    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No tasks found</h3>
    <p class="text-muted">Create your first task to get started!</p>
    <a href="{{ url_for('create_task') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Create Task
    </a>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// Global variables for status checking
let statusCheckInProgress = false;
let lastKnownStatus = null;
let consecutiveFailures = 0;

// Check WAHA status with improved reliability
function checkWahaStatus() {
    // Prevent multiple simultaneous requests
    if (statusCheckInProgress) {
        console.log('Status check already in progress, skipping...');
        return;
    }

    statusCheckInProgress = true;

    // Use longer timeout and add retry logic
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

    fetch('/waha/status', {
        signal: controller.signal,
        cache: 'no-cache',
        headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            const statusElement = document.getElementById('waha-status');
            const restartBtn = document.getElementById('restart-waha-btn');

            // Reset failure counter on success
            consecutiveFailures = 0;

            if (data.waha_connected === true) {
                lastKnownStatus = 'connected';
                statusElement.innerHTML = '<span class="badge bg-success"><i class="fab fa-whatsapp"></i> <span class="d-none d-sm-inline">WhatsApp </span>Connected</span>';
                restartBtn.style.display = 'none';
            } else if (data.waha_connected === false) {
                lastKnownStatus = 'disconnected';
                statusElement.innerHTML = '<span class="badge bg-danger"><i class="fab fa-whatsapp"></i> <span class="d-none d-sm-inline">WhatsApp </span>Disconnected</span>';
                restartBtn.style.display = 'inline-block';
            } else {
                lastKnownStatus = 'error';
                statusElement.innerHTML = '<span class="badge bg-warning"><i class="fab fa-whatsapp"></i> <span class="d-none d-sm-inline">WhatsApp </span>Status Error</span>';
                restartBtn.style.display = 'inline-block';
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            consecutiveFailures++;

            console.error('WAHA status check error:', error);

            const statusElement = document.getElementById('waha-status');
            const restartBtn = document.getElementById('restart-waha-btn');

            // Only show "Status Check Failed" after multiple consecutive failures
            // This prevents temporary network hiccups from showing as failures
            if (consecutiveFailures >= 2) {
                statusElement.innerHTML = '<span class="badge bg-warning"><i class="fab fa-whatsapp"></i> <span class="d-none d-sm-inline">Status </span>Check Failed</span>';
                restartBtn.style.display = 'inline-block';
            } else {
                // Keep showing last known status for single failures
                console.log(`Temporary failure ${consecutiveFailures}/2, keeping last known status`);
                if (lastKnownStatus === 'connected') {
                    statusElement.innerHTML = '<span class="badge bg-success"><i class="fab fa-whatsapp"></i> <span class="d-none d-sm-inline">WhatsApp </span>Connected</span>';
                    restartBtn.style.display = 'none';
                } else if (lastKnownStatus === 'disconnected') {
                    statusElement.innerHTML = '<span class="badge bg-danger"><i class="fab fa-whatsapp"></i> <span class="d-none d-sm-inline">WhatsApp </span>Disconnected</span>';
                    restartBtn.style.display = 'inline-block';
                }
            }
        })
        .finally(() => {
            statusCheckInProgress = false;
        });
}

// Silent task status update without page refresh
function updateTaskStatusSilently() {
    fetch('/api/task-status', {
        method: 'GET',
        cache: 'no-cache'
    })
    .then(response => response.json())
    .then(data => {
        // Update task status badges silently
        if (data.tasks) {
            data.tasks.forEach(task => {
                const statusElement = document.querySelector(`[data-task-id="${task.id}"] .task-status`);
                if (statusElement) {
                    if (task.completed) {
                        statusElement.innerHTML = '<span class="badge bg-success"><i class="fas fa-check"></i> Completed</span>';
                    } else {
                        statusElement.innerHTML = '<span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>';
                    }
                }
            });
        }
    })
    .catch(error => {
        console.log('Silent task update failed:', error);
        // Fail silently - don't show errors to user
    });
}

// Restart WAHA session
function restartWaha() {
    const restartBtn = document.getElementById('restart-waha-btn');
    const statusElement = document.getElementById('waha-status');

    // Show loading state
    restartBtn.disabled = true;
    restartBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Restarting...';
    statusElement.innerHTML = '<span class="badge bg-warning"><i class="fab fa-whatsapp"></i> Restarting WhatsApp...</span>';

    fetch('/waha/restart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusElement.innerHTML = '<span class="badge bg-info"><i class="fab fa-whatsapp"></i> WhatsApp Restart Initiated</span>';
            // Check status again after a delay
            setTimeout(checkWahaStatus, 3000);
        } else {
            statusElement.innerHTML = '<span class="badge bg-danger"><i class="fab fa-whatsapp"></i> Restart Failed</span>';
        }
    })
    .catch(error => {
        statusElement.innerHTML = '<span class="badge bg-danger"><i class="fab fa-whatsapp"></i> Restart Error</span>';
    })
    .finally(() => {
        // Reset button
        restartBtn.disabled = false;
        restartBtn.innerHTML = '<i class="fas fa-redo"></i> Restart WhatsApp';
    });
}

// Check status on page load
checkWahaStatus();

// Check WAHA status every 15 seconds (no auto-refresh to prevent page blinking)
setInterval(checkWahaStatus, 15000);

// Optional: Update task status silently every 2 minutes without page refresh
setInterval(function() {
    updateTaskStatusSilently();
}, 120000);
</script>
{% endblock %}
