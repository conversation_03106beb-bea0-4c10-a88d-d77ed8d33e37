# PowerShell script to install the self-signed certificate to Windows trust store
# This will make browsers trust the certificate

Write-Host "Installing SSL certificate to Windows trust store..." -ForegroundColor Green

$certPath = "certs\cert.pem"

if (-not (Test-Path $certPath)) {
    Write-Host "Certificate file not found: $certPath" -ForegroundColor Red
    exit 1
}

try {
    # Convert PEM to CER format for Windows
    $cerPath = "certs\cert.cer"
    openssl x509 -outform der -in $certPath -out $cerPath
    
    if (-not (Test-Path $cerPath)) {
        Write-Host "Failed to convert certificate to CER format" -ForegroundColor Red
        exit 1
    }
    
    # Import certificate to Local Machine Trusted Root Certification Authorities
    Write-Host "Installing certificate to Trusted Root Certification Authorities..." -ForegroundColor Yellow
    
    $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
    $cert.Import($cerPath)
    
    $store = New-Object System.Security.Cryptography.X509Certificates.X509Store("Root", "LocalMachine")
    $store.Open("ReadWrite")
    $store.Add($cert)
    $store.Close()
    
    Write-Host "Certificate installed successfully!" -ForegroundColor Green
    Write-Host "You may need to restart your browser for changes to take effect." -ForegroundColor Yellow
    
    # Clean up temporary CER file
    Remove-Item $cerPath -Force
    
} catch {
    Write-Host "Error installing certificate: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to run this script as Administrator" -ForegroundColor Yellow
}
