"""
Simple example showing how to use the web search API.
This simulates what your n8n workflow would do.
"""

import requests
import json

def call_web_search_api(query):
    """
    Call the web search API - this is what your n8n HTTP Request node would do.
    """
    url = "http://localhost:5555/web-search"

    payload = {
        "query": query
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"🔍 Searching for: '{query}'")
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        return None


def process_search_results(results):
    """
    Process the search results - this is what you'd do in n8n after getting the response.
    """
    if not results or results.get('status') != 'success':
        print("❌ No valid results received")
        return []
    
    processed_data = []
    
    print(f"\n📊 Processing {results['total_results']} results...")
    print(f"⏱️ Search took {results['processing_time']} seconds")
    print(f"✅ {results['successful_fetches']} successful content fetches")
    
    for i, result in enumerate(results.get('results', []), 1):
        print(f"\n--- Processing Result {i} ---")
        
        # Extract the data you need
        item = {
            'title': result.get('fetched_title') or result.get('search_title', 'No Title'),
            'url': result.get('url'),
            'snippet': result.get('snippet', ''),
            'content': result.get('content'),
            'content_length': result.get('content_length', 0),
            'fetch_successful': result.get('fetch_status') == 'success'
        }
        
        print(f"Title: {item['title']}")
        print(f"URL: {item['url']}")
        print(f"Content Length: {item['content_length']} characters")
        print(f"Fetch Status: {'✅ Success' if item['fetch_successful'] else '❌ Failed'}")
        
        if not item['fetch_successful']:
            print(f"Error: {result.get('fetch_error', 'Unknown error')}")
        elif item['content']:
            # Show first 150 characters of content
            preview = item['content'][:150].replace('\n', ' ')
            print(f"Content Preview: {preview}...")
        
        processed_data.append(item)
    
    return processed_data


def main():
    """
    Example usage - this simulates your n8n workflow.
    """
    print("🚀 n8n Web Search API Example")
    print("=" * 50)
    
    # Example 1: Search for AI news
    print("\n📰 Example 1: Searching for AI news")
    results = call_web_search_api("latest artificial intelligence news 2024")
    if results:
        ai_data = process_search_results(results)
        print(f"\n✅ Processed {len(ai_data)} AI news items")

    # Example 2: Search for technical information
    print("\n" + "=" * 50)
    print("\n🔧 Example 2: Searching for technical information")
    results = call_web_search_api("Docker container best practices")
    if results:
        tech_data = process_search_results(results)
        print(f"\n✅ Processed {len(tech_data)} technical articles")

    # Example 3: Search for specific information
    print("\n" + "=" * 50)
    print("\n🎯 Example 3: Specific search")
    results = call_web_search_api("OpenAI GPT-4 documentation")
    if results:
        single_data = process_search_results(results)
        print(f"\n✅ Processed {len(single_data)} results")
    
    print("\n" + "=" * 50)
    print("✅ Examples completed!")
    print("\nIn your n8n workflow, you would:")
    print("1. Use HTTP Request node to call the API")
    print("2. Use Set node to extract the data you need")
    print("3. Use IF node to filter successful fetches")
    print("4. Use the content for your AI agent processing")


if __name__ == "__main__":
    main()
