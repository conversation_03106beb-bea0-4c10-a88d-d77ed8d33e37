#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to recreate ChromaDB collection for switching embedding models.
This will delete the existing collection and create a new one that supports
1024-dimensional embeddings (snowflake-artic-embed2:568m).

WARNING: This will delete all existing data in the collection!
Make sure to backup your data first if needed.
"""

import chromadb
from chromadb.config import Settings
import sys
import os

# Configuration
COLLECTION_NAME = "my_pdf_collection"
CHROMA_DATA_PATH = "./chroma_data"

def backup_collection_data():
    """Export existing collection data before deletion."""
    try:
        print("🔄 Creating backup of existing collection...")
        
        # Connect to existing collection
        client = chromadb.PersistentClient(
            path=CHROMA_DATA_PATH,
            settings=Settings(
                chroma_client_auth_provider="chromadb.auth.token.TokenAuthClientProvider",
                chroma_client_auth_credentials="your-token-here"
            )
        )
        
        try:
            collection = client.get_collection(name=COLLECTION_NAME)
            
            # Get all data
            results = collection.get(include=["documents", "metadatas", "embeddings"])
            
            if results and results.get("ids"):
                print(f"📊 Found {len(results['ids'])} documents to backup")
                
                # Save backup data
                import json
                from datetime import datetime
                
                backup_filename = f"collection_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                
                backup_data = {
                    "collection_name": COLLECTION_NAME,
                    "backup_date": datetime.now().isoformat(),
                    "total_documents": len(results["ids"]),
                    "embedding_dimension": len(results["embeddings"][0]) if results.get("embeddings") and results["embeddings"] else None,
                    "data": {
                        "ids": results.get("ids", []),
                        "documents": results.get("documents", []),
                        "metadatas": results.get("metadatas", [])
                        # Note: Not backing up embeddings as they need to be regenerated with new model
                    }
                }
                
                with open(backup_filename, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)
                
                print(f"✅ Backup saved to: {backup_filename}")
                print(f"📏 Original embedding dimension: {backup_data['embedding_dimension']}")
                return backup_filename
            else:
                print("📭 Collection is empty, no backup needed")
                return None
                
        except Exception as e:
            print(f"⚠️  Collection doesn't exist or is empty: {e}")
            return None
            
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return None

def delete_collection():
    """Delete the existing collection."""
    try:
        print("🗑️  Deleting existing collection...")
        
        client = chromadb.PersistentClient(
            path=CHROMA_DATA_PATH,
            settings=Settings(
                chroma_client_auth_provider="chromadb.auth.token.TokenAuthClientProvider",
                chroma_client_auth_credentials="your-token-here"
            )
        )
        
        try:
            client.delete_collection(name=COLLECTION_NAME)
            print("✅ Collection deleted successfully")
            return True
        except Exception as e:
            print(f"⚠️  Collection might not exist: {e}")
            return True  # Continue anyway
            
    except Exception as e:
        print(f"❌ Error deleting collection: {e}")
        return False

def create_new_collection():
    """Create a new collection that will support 1024-dimensional embeddings."""
    try:
        print("🆕 Creating new collection...")
        
        client = chromadb.PersistentClient(
            path=CHROMA_DATA_PATH,
            settings=Settings(
                chroma_client_auth_provider="chromadb.auth.token.TokenAuthClientProvider",
                chroma_client_auth_credentials="your-token-here"
            )
        )
        
        # Create new collection
        collection = client.get_or_create_collection(name=COLLECTION_NAME)
        
        print("✅ New collection created successfully")
        print("📏 New collection will support 1024-dimensional embeddings (snowflake-artic-embed2:568m)")
        return True
        
    except Exception as e:
        print(f"❌ Error creating new collection: {e}")
        return False

def main():
    """Main function to recreate the collection."""
    print("🔄 ChromaDB Collection Recreation Script")
    print("=" * 50)
    print("⚠️  WARNING: This will delete all existing data!")
    print("📋 This script will:")
    print("   1. Backup existing collection data (documents & metadata only)")
    print("   2. Delete the existing collection")
    print("   3. Create a new collection for 1024-dimensional embeddings")
    print("   4. You'll need to re-process your documents with the new embedding model")
    print()
    
    # Confirm with user
    response = input("Do you want to continue? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("❌ Operation cancelled")
        return
    
    print("\n🚀 Starting collection recreation...")
    
    # Step 1: Backup
    backup_file = backup_collection_data()
    
    # Step 2: Delete existing collection
    if not delete_collection():
        print("❌ Failed to delete collection. Aborting.")
        return
    
    # Step 3: Create new collection
    if not create_new_collection():
        print("❌ Failed to create new collection. Aborting.")
        return
    
    print("\n🎉 Collection recreation completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Update your n8n workflow to use 'snowflake-artic-embed2:568m'")
    print("   2. Re-upload your documents to generate new 1024-dimensional embeddings")
    if backup_file:
        print(f"   3. Your old data is backed up in: {backup_file}")
    print("   4. Restart your Flask server")

if __name__ == "__main__":
    main()
