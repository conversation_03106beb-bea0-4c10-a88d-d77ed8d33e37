# Delete Documents by Source API

This document describes the new API endpoint for deleting all documents from a specific source file in the ChromaDB collection.

## Endpoint Details

**URL:** `/api/documents_by_source`  
**Method:** `DELETE`  
**Content-Type:** `application/json`

## Request Format

### Request Body
```json
{
    "source": "filename.pdf"
}
```

### Parameters
- `source` (string, required): The exact filename of the source to delete documents from

## Response Formats

### Success Response (200)
```json
{
    "status": "success",
    "message": "Successfully deleted source 'filename.pdf'",
    "deleted_count": 25,
    "source": "filename.pdf"
}
```

### Error Response - Missing Source (400)
```json
{
    "status": "error",
    "error": "source required"
}
```

### Error Response - Source Not Found (400)
```json
{
    "status": "error",
    "error": "Source not found"
}
```

### Error Response - Server Error (500)
```json
{
    "status": "error",
    "error": "Internal server error message"
}
```

## Functionality

### What it does:
1. **Find all documents** where metadata `Source` field matches the provided source name
2. **Delete all matching documents** from the ChromaDB collection
3. **Return count** of deleted documents and confirmation
4. **Handle errors** appropriately for missing or non-existent sources

### What it doesn't do:
- Does not delete partial matches (exact source name match only)
- Does not delete documents without Source metadata
- Does not provide undo functionality

## Usage Examples

### Using curl
```bash
# Delete all documents from a specific PDF
curl -X DELETE http://localhost:5555/api/documents_by_source \
  -H "Content-Type: application/json" \
  -d '{"source": "example-document.pdf"}'
```

### Using Python requests
```python
import requests

url = "http://localhost:5555/api/documents_by_source"
data = {"source": "example-document.pdf"}

response = requests.delete(url, json=data)
result = response.json()

if response.status_code == 200:
    print(f"Deleted {result['deleted_count']} documents")
else:
    print(f"Error: {result['error']}")
```

### Using JavaScript fetch
```javascript
const response = await fetch('http://localhost:5555/api/documents_by_source', {
    method: 'DELETE',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        source: 'example-document.pdf'
    })
});

const result = await response.json();

if (response.ok) {
    console.log(`Deleted ${result.deleted_count} documents`);
} else {
    console.error(`Error: ${result.error}`);
}
```

## Implementation Details

### Database Query
The endpoint uses ChromaDB's `where` clause to filter documents:
```python
results = collection.get(
    where={"Source": source},
    include=["metadatas"]
)
```

### Deletion Process
1. Query for documents with matching Source metadata
2. Extract document IDs from query results
3. Delete all documents using the collected IDs
4. Return success response with deletion count

### Error Handling
- **Input validation**: Checks for required `source` parameter
- **Source validation**: Verifies that documents exist for the given source
- **Database errors**: Catches and handles ChromaDB exceptions
- **Logging**: All operations are logged for debugging

## Security Considerations

### Access Control
- No authentication required (matches existing API design)
- Consider adding authentication for production use

### Data Safety
- **Irreversible operation**: Deleted documents cannot be recovered
- **Exact match only**: Reduces risk of accidental bulk deletions
- **Confirmation required**: Frontend should implement confirmation dialogs

### Rate Limiting
- Consider implementing rate limiting for bulk operations
- Monitor for abuse patterns

## Testing

### Manual Testing
The endpoint can be tested manually using curl, Python requests, or JavaScript fetch as shown in the usage examples above.

### Test Cases to Verify
- ✅ Missing source parameter
- ✅ Non-existent source
- ✅ Valid source deletion
- ✅ Response format validation
- ✅ Document count verification

## Integration Notes

### Frontend Integration
The endpoint is designed to work with the existing web interface. Consider adding:
- Confirmation dialogs before deletion
- Progress indicators for large deletions
- Success/error notifications

### API Consistency
- Follows existing API patterns in the application
- Uses consistent error response format
- Maintains logging standards

## Monitoring and Logging

### Log Messages
- Info: Deletion attempts and success
- Error: Failed operations and exceptions
- Debug: Query results and document counts

### Metrics to Monitor
- Deletion frequency per source
- Error rates
- Response times for large deletions

## Future Enhancements

### Potential Improvements
1. **Batch deletion**: Support multiple sources in one request
2. **Soft delete**: Mark documents as deleted instead of permanent removal
3. **Backup**: Create backups before deletion
4. **Audit trail**: Track who deleted what and when
5. **Partial matching**: Support pattern-based source matching

### API Versioning
Consider versioning if significant changes are needed:
- `/api/v1/documents_by_source`
- `/api/v2/documents_by_source`
