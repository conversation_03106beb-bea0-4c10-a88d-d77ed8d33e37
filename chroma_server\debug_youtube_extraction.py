#!/usr/bin/env python3
"""
Debug script to simulate the exact same process as pasting a YouTube link into the UI.
This will test the complete flow: enqueue -> queue processing -> transcript extraction -> n8n workflow.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import time
import json
from src.routes.youtube_routes import _extract_youtube_transcript_if_needed, enqueue_youtube_link
from src.flows.process_queue import QueuedProcess, ProcessType, queue_manager
from src.core.config import RAPIDAPI_KEY
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test URL - the one you specified
TEST_VIDEO_URL = "https://www.youtube.com/watch?v=9IZ_izMtBaU"

def test_direct_api_call():
    """Test the RapidAPI call directly to see what we get for the specific video."""
    print("🔍 Step 1: Testing direct RapidAPI call...")
    print(f"Video URL: {TEST_VIDEO_URL}")

    import http.client
    import json
    from urllib.parse import urlparse, parse_qs

    # Extract video ID
    parsed = urlparse(TEST_VIDEO_URL)
    video_id = None

    if parsed.hostname and "youtu.be" in parsed.hostname:
        video_id = parsed.path.lstrip("/")
    elif parsed.hostname and "youtube.com" in parsed.hostname:
        video_id = parse_qs(parsed.query).get("v", [None])[0]

    if not video_id:
        print(f"❌ Could not extract video ID from URL: {TEST_VIDEO_URL}")
        return False

    print(f"📺 Video ID: {video_id}")

    try:
        conn = http.client.HTTPSConnection("youtube-transcriptor.p.rapidapi.com")
        headers = {
            'x-rapidapi-key': RAPIDAPI_KEY,
            'x-rapidapi-host': "youtube-transcriptor.p.rapidapi.com"
        }
        endpoint = f"/transcript?video_id={video_id}&lang=en"

        print(f"🌐 Making API request to: youtube-transcriptor.p.rapidapi.com{endpoint}")

        conn.request("GET", endpoint, headers=headers)
        res = conn.getresponse()
        data = res.read()

        print(f"📊 API response status: {res.status}")

        if res.status == 200:
            payload = json.loads(data.decode("utf-8"))
            data_obj = payload[0] if isinstance(payload, list) and payload else payload

            print(f"📋 Response keys: {list(data_obj.keys()) if isinstance(data_obj, dict) else 'Not a dict'}")

            if 'transcriptionAsText' in data_obj:
                transcript = data_obj['transcriptionAsText'].strip()
                print(f"📝 Transcript length: {len(transcript)} characters")
                print(f"📄 Preview: {transcript[:100]}...")
                print(f"❓ Is empty: {len(transcript) == 0}")
                return transcript  # Return empty string, not None
            else:
                print("❌ No 'transcriptionAsText' field found")
                return None
        else:
            error_msg = data.decode("utf-8")
            print(f"❌ API error: {res.status} - {error_msg}")
            return None

    except Exception as e:
        print(f"❌ Exception during API call: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return None


def test_server_endpoint():
    """Test the server endpoint exactly like the UI does."""
    print("\n🔍 Step 2: Testing server endpoint (like UI)...")

    try:
        # This is exactly what the UI does
        payload = {
            "video_url": TEST_VIDEO_URL,
            "video_title": "Test Video from UI Simulation",
            "source": "youtube_transcripts"
        }

        print(f"📤 Sending request to: http://localhost:5555/enqueue-youtube-link")
        print(f"📦 Payload: {json.dumps(payload, indent=2)}")

        response = requests.post(
            'http://localhost:5555/enqueue-youtube-link',
            json=payload,
            timeout=30
        )

        print(f"📊 Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Enqueue successful!")
            print(f"📋 Response: {json.dumps(data, indent=2)}")

            session_id = data['data']['session_id']
            print(f"🆔 Session ID: {session_id}")

            # Wait and check status multiple times
            for i in range(6):  # Check 6 times over 12 seconds
                time.sleep(2)
                print(f"\n⏱️  Checking status (attempt {i+1}/6)...")

                status_response = requests.get(f'http://localhost:5555/api/session-status/{session_id}')
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    session_info = status_data.get('session', {})

                    print(f"📊 Status: {session_info.get('status')}")
                    print(f"📝 Error: {session_info.get('error_message', 'None')}")
                    print(f"📦 Chunks: {session_info.get('completed_chunks', 0)}/{session_info.get('total_chunks', 0)}")

                    if session_info.get('status') in ['completed', 'failed']:
                        print(f"🏁 Final status: {session_info.get('status')}")
                        if session_info.get('status') == 'failed':
                            print(f"❌ Error message: {session_info.get('error_message')}")
                        return session_info.get('status') == 'completed'
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")

            print("⏰ Timeout waiting for completion")
            return False

        else:
            print(f"❌ Enqueue failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Exception during server test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False


def test_complete_flow():
    """Test the complete flow exactly as it happens in the UI."""
    print("🚀 Testing complete YouTube processing flow")
    print("=" * 60)

    # Step 1: Test direct API call
    transcript = test_direct_api_call()
    if transcript is None:
        print("\n❌ Direct API call failed - stopping test")
        return False
    elif transcript == "":
        print(f"\n⚠️  Direct API call successful but transcript is empty - continuing test")
    else:
        print(f"\n✅ Direct API call successful - transcript length: {len(transcript)}")

    # Step 2: Test server endpoint
    success = test_server_endpoint()

    print("\n" + "=" * 60)
    if success:
        print("🎉 Complete flow test PASSED!")
    else:
        print("❌ Complete flow test FAILED!")

    return success

if __name__ == "__main__":
    test_complete_flow()
