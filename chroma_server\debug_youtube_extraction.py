#!/usr/bin/env python3
"""
Debug script to test YouTube transcript extraction directly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.routes.youtube_routes import _extract_youtube_transcript_if_needed
from src.flows.process_queue import QueuedProcess, ProcessType
from src.core.config import RAPIDAPI_KEY
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_youtube_extraction():
    """Test YouTube transcript extraction with a mock process."""

    print("🔍 Testing YouTube transcript extraction...")
    print(f"RapidAPI Key: {RAPIDAPI_KEY[:10]}...")

    # Create a mock process similar to what the queue manager would create
    mock_process = QueuedProcess(
        session_id="test-session-123",
        process_type=ProcessType.YOUTUBE,
        filename="Test Video.yt",
        content="",  # Empty content for deferred extraction
        metadata={
            "source": "test",
            "video_url": "https://youtube.com/watch?v=dQw4w9WgXcQ",
            "processing_method": "youtube_link_deferred",
            "deferred_youtube": True,
            "rapidapi_key": RAPIDAPI_KEY,
        },
        created_at=datetime.now()
    )

    print(f"Mock process metadata: {mock_process.metadata}")
    print(f"Video URL: {mock_process.metadata.get('video_url')}")
    print(f"Deferred YouTube: {mock_process.metadata.get('deferred_youtube')}")
    print(f"RapidAPI key in metadata: {'rapidapi_key' in mock_process.metadata}")

    # Test the extraction function
    try:
        print("\n🔄 Calling _extract_youtube_transcript_if_needed...")
        result = _extract_youtube_transcript_if_needed(mock_process, logger)

        print(f"\n📊 Function returned: {result} (type: {type(result)})")

        if result:
            print("✅ Extraction successful!")
            print(f"Content length: {len(mock_process.content)}")
            print(f"Content preview: {mock_process.content[:200]}...")
        else:
            print("❌ Extraction failed!")

        # Now test the n8n workflow trigger
        print("\n🔄 Testing n8n workflow trigger...")
        from src.flows.ingestion import trigger_n8n_workflow

        if mock_process.content:
            workflow_triggered, returned_session_id, total_chunks = trigger_n8n_workflow(
                mock_process.content, mock_process.filename, session_id=mock_process.session_id
            )
            print(f"N8N workflow triggered: {workflow_triggered}")
            print(f"Total chunks: {total_chunks}")
            print(f"Returned session ID: {returned_session_id}")
        else:
            print("❌ No content to send to n8n workflow")

        return result

    except Exception as e:
        print(f"❌ Exception during extraction: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_youtube_extraction()
