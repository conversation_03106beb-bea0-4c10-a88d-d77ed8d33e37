"""
Process Queue Manager for limiting concurrent n8n workflows.

This module manages a queue system that limits concurrent n8n processes to a maximum of 3,
with automatic queuing and processing of pending uploads.
"""

import threading
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

# Import state manager for status updates
from ..core.state import state_manager, UploadStatus
from ..core.utils import send_upload_status_notification

logger = logging.getLogger(__name__)


class ProcessType(Enum):
    """Types of processes that can be queued."""
    PDF = "pdf"
    YOUTUBE = "youtube"


class ProcessStatus(Enum):
    """Status of a process in the queue system."""
    QUEUED = "queued"
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class QueuedProcess:
    """Represents a process in the queue."""
    session_id: str
    process_type: ProcessType
    filename: str
    content: str  # Extracted text content
    metadata: Dict
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: ProcessStatus = ProcessStatus.QUEUED
    total_chunks: int = 0
    completed_chunks: int = 0
    error_message: Optional[str] = None


class ProcessQueueManager:
    """
    Manages a queue of processes with a maximum of 3 concurrent n8n workflows.
    
    Features:
    - Maximum 3 concurrent processes
    - FIFO queue for pending processes
    - Automatic queue progression
    - Session and chunk tracking
    - Thread-safe operations
    """
    
    def __init__(self, max_concurrent_processes: int = 3):
        self.max_concurrent_processes = max_concurrent_processes
        self.active_processes: Dict[str, QueuedProcess] = {}
        self.pending_queue: List[QueuedProcess] = []
        self.completed_processes: Dict[str, QueuedProcess] = {}
        self.lock = threading.Lock()
        
        logger.info(f"ProcessQueueManager initialized with max {max_concurrent_processes} concurrent processes")
    
    def add_to_queue(self, process_type: ProcessType, filename: str, content: str, 
                     metadata: Dict, session_id: Optional[str] = None) -> Tuple[str, bool]:
        """
        Add a new process to the queue.
        
        Args:
            process_type: Type of process (PDF or YouTube)
            filename: Name of the file/source
            content: Extracted text content
            metadata: Process metadata
            session_id: Optional session ID (will generate if not provided)
        
        Returns:
            Tuple of (session_id, started_immediately)
        """
        with self.lock:
            if session_id is None:
                session_id = str(uuid.uuid4())
            
            process = QueuedProcess(
                session_id=session_id,
                process_type=process_type,
                filename=filename,
                content=content,
                metadata=metadata,
                created_at=datetime.now()
            )
            
            # Check if we can start immediately
            if len(self.active_processes) < self.max_concurrent_processes:
                # Update state to ACTIVE immediately
                try:
                    state_manager.create_session(session_id, filename, process_type.value, metadata)
                    state_manager.update_session_status(session_id, UploadStatus.ACTIVE, "Processing started")
                except Exception:
                    pass
                return self._start_process_immediately(process)
            else:
                # Add to pending queue
                self.pending_queue.append(process)
                position = len(self.pending_queue)
                logger.info(f"Process {session_id} ({filename}) added to queue. Position: {position}")
                try:
                    state_manager.create_session(session_id, filename, process_type.value, metadata)
                    state_manager.update_queue_position(session_id, position)
                except Exception:
                    pass
                return session_id, False

    def _start_process_immediately(self, process: QueuedProcess) -> Tuple[str, bool]:
        """Start a process immediately without queuing."""
        process.status = ProcessStatus.ACTIVE
        process.started_at = datetime.now()
        self.active_processes[process.session_id] = process

        logger.info(f"Process {process.session_id} ({process.filename}) started immediately. "
                   f"Active processes: {len(self.active_processes)}/{self.max_concurrent_processes}")

        # Trigger the n8n workflow for this process
        self._trigger_n8n_workflow(process)

        # Reflect ACTIVE status in state manager and push to frontend
        try:
            state_manager.update_session_status(process.session_id, UploadStatus.ACTIVE, "Processing started")
            send_upload_status_notification({
                "endpoint_type": process.process_type.value,
                "status": "processing",
                "filename": process.filename,
                "session_id": process.session_id,
                "message": f"Started processing {process.filename}"
            })
        except Exception:
            pass

        return process.session_id, True

    def mark_process_completed(self, session_id: str, success: bool = True, 
                             error_message: Optional[str] = None) -> bool:
        """
        Mark a process as completed and start the next queued process if available.
        
        Args:
            session_id: Session ID of the completed process
            success: Whether the process completed successfully
            error_message: Error message if the process failed
        
        Returns:
            True if process was found and marked as completed
        """
        with self.lock:
            if session_id not in self.active_processes:
                logger.warning(f"Attempted to complete unknown session: {session_id}")
                return False
            
            process = self.active_processes.pop(session_id)
            process.completed_at = datetime.now()
            process.status = ProcessStatus.COMPLETED if success else ProcessStatus.FAILED

            if error_message:
                process.error_message = error_message

            self.completed_processes[session_id] = process

            logger.info(f"Process {session_id} ({process.filename}) completed. "
                       f"Status: {process.status.value}. "
                       f"Active processes: {len(self.active_processes)}/{self.max_concurrent_processes}")

            # Reflect completion in state manager
            try:
                if success:
                    state_manager.update_session_status(session_id, UploadStatus.COMPLETED, "Processing completed successfully")
                else:
                    state_manager.update_session_status(session_id, UploadStatus.FAILED, error_message or "Processing failed", error_message=error_message)
            except Exception:
                pass

            # Start next queued process if available
            self._start_next_queued_process()

            return True

    def _start_next_queued_process(self):
        """Start the next process in the queue if there's capacity."""
        if self.pending_queue and len(self.active_processes) < self.max_concurrent_processes:
            next_process = self.pending_queue.pop(0)
            next_process.status = ProcessStatus.ACTIVE
            next_process.started_at = datetime.now()
            self.active_processes[next_process.session_id] = next_process

            logger.info(f"Started queued process {next_process.session_id} ({next_process.filename}). "
                       f"Remaining in queue: {len(self.pending_queue)}")

            # Trigger the actual n8n workflow for this process
            self._trigger_n8n_workflow(next_process)

            # Update state to ACTIVE and update queue positions for remaining
            try:
                state_manager.update_session_status(next_process.session_id, UploadStatus.ACTIVE, "Processing started")
                # Recompute positions for remaining queued processes
                for idx, p in enumerate(self.pending_queue, start=1):
                    state_manager.update_queue_position(p.session_id, idx)
            except Exception:
                pass

    def _trigger_n8n_workflow(self, process: QueuedProcess):
        """Trigger the n8n workflow for a process."""
        try:
            # Import here to avoid circular imports
            from .ingestion import trigger_n8n_workflow, _extract_pdf_content, pre_chunk_text, estimate_token_count
            from ..routes.youtube_routes import _extract_youtube_transcript_if_needed

            logger.info(f"Triggering n8n workflow for {process.session_id} ({process.filename})")

            # Trigger the workflow in a separate thread to avoid blocking
            def trigger_workflow():
                try:
                    # If this is a deferred PDF job, extract now using cached file
                    if process.metadata.get("deferred_pdf") and process.metadata.get("file_path"):
                        file_path = process.metadata.get("file_path")
                        logger.info(f"Deferred PDF extraction starting for session {process.session_id} from {file_path}")
                        try:
                            extracted_text = _extract_pdf_content(file_path)
                            # Update metadata and content for this process
                            process.content = extracted_text
                            # Optional stats
                            total_tokens = estimate_token_count(extracted_text)
                            text_chunks = pre_chunk_text(extracted_text)
                            with self.lock:
                                if process.session_id in self.active_processes:
                                    self.active_processes[process.session_id].total_chunks = len(text_chunks)
                        finally:
                            # Clean up cached file
                            import os
                            try:
                                os.unlink(file_path)
                                logger.info(f"Deleted cached PDF file {file_path} for session {process.session_id}")
                            except Exception:
                                logger.warning(f"Failed to delete cached PDF file {file_path}")

                    # If this is a deferred YouTube job, extract transcript now
                    if process.metadata.get("deferred_youtube"):
                        ok = _extract_youtube_transcript_if_needed(process, logger)
                        if not ok:
                            raise Exception("Failed to extract YouTube transcript")

                    # Proceed to trigger n8n with the (possibly newly extracted) content
                    workflow_triggered, returned_session_id, total_chunks = trigger_n8n_workflow(
                        process.content, process.filename, session_id=process.session_id
                    )

                    if workflow_triggered:
                        with self.lock:
                            if process.session_id in self.active_processes:
                                self.active_processes[process.session_id].total_chunks = total_chunks
                        logger.info(f"n8n workflow triggered for {process.session_id}. Total chunks: {total_chunks}")
                    else:
                        logger.error(f"Failed to trigger n8n workflow for {process.session_id}")
                        self.mark_process_completed(process.session_id, success=False,
                                                  error_message="Failed to trigger n8n workflow")
                except Exception as e:
                    logger.error(f"Error triggering n8n workflow for {process.session_id}: {e}")
                    self.mark_process_completed(process.session_id, success=False,
                                              error_message=str(e))

            threading.Thread(target=trigger_workflow, daemon=True).start()

        except Exception as e:
            logger.error(f"Error setting up n8n workflow trigger for {process.session_id}: {e}")
            self.mark_process_completed(process.session_id, success=False, error_message=str(e))
    
    def update_chunk_progress(self, session_id: str, completed_chunks: int) -> bool:
        """
        Update the chunk progress for an active process.
        
        Args:
            session_id: Session ID of the process
            completed_chunks: Number of completed chunks
        
        Returns:
            True if process was found and updated
        """
        with self.lock:
            if session_id in self.active_processes:
                process = self.active_processes[session_id]
                process.completed_chunks = completed_chunks
                
                logger.debug(f"Updated chunk progress for {session_id}: "
                           f"{completed_chunks}/{process.total_chunks}")
                
                # Check if all chunks are completed
                if process.total_chunks > 0 and completed_chunks >= process.total_chunks:
                    logger.info(f"All chunks completed for {session_id}. Marking as completed.")
                    # Don't call mark_process_completed here to avoid deadlock
                    # This will be called from the callback endpoint
                
                return True
            
            return False
    
    def get_queue_status(self) -> Dict:
        """Get the current status of the queue system."""
        with self.lock:
            active_list = []
            for process in self.active_processes.values():
                active_list.append({
                    "session_id": process.session_id,
                    "filename": process.filename,
                    "type": process.process_type.value,
                    "started_at": process.started_at.isoformat() if process.started_at else None,
                    "total_chunks": process.total_chunks,
                    "completed_chunks": process.completed_chunks,
                    "progress": (process.completed_chunks / process.total_chunks * 100) 
                               if process.total_chunks > 0 else 0
                })
            
            pending_list = []
            for i, process in enumerate(self.pending_queue):
                pending_list.append({
                    "session_id": process.session_id,
                    "filename": process.filename,
                    "type": process.process_type.value,
                    "position": i + 1,
                    "created_at": process.created_at.isoformat()
                })
            
            return {
                "active_processes": active_list,
                "pending_queue": pending_list,
                "active_count": len(self.active_processes),
                "pending_count": len(self.pending_queue),
                "max_concurrent": self.max_concurrent_processes,
                "available_slots": self.max_concurrent_processes - len(self.active_processes)
            }
    
    def get_process_status(self, session_id: str) -> Optional[Dict]:
        """Get the status of a specific process."""
        with self.lock:
            # Check active processes
            if session_id in self.active_processes:
                process = self.active_processes[session_id]
                return {
                    "session_id": session_id,
                    "status": process.status.value,
                    "filename": process.filename,
                    "type": process.process_type.value,
                    "started_at": process.started_at.isoformat() if process.started_at else None,
                    "total_chunks": process.total_chunks,
                    "completed_chunks": process.completed_chunks,
                    "progress": (process.completed_chunks / process.total_chunks * 100) 
                               if process.total_chunks > 0 else 0
                }
            
            # Check pending queue
            for i, process in enumerate(self.pending_queue):
                if process.session_id == session_id:
                    return {
                        "session_id": session_id,
                        "status": process.status.value,
                        "filename": process.filename,
                        "type": process.process_type.value,
                        "position": i + 1,
                        "created_at": process.created_at.isoformat()
                    }
            
            # Check completed processes
            if session_id in self.completed_processes:
                process = self.completed_processes[session_id]
                return {
                    "session_id": session_id,
                    "status": process.status.value,
                    "filename": process.filename,
                    "type": process.process_type.value,
                    "started_at": process.started_at.isoformat() if process.started_at else None,
                    "completed_at": process.completed_at.isoformat() if process.completed_at else None,
                    "total_chunks": process.total_chunks,
                    "completed_chunks": process.completed_chunks,
                    "error_message": process.error_message
                }
            
            return None


# Global queue manager instance
queue_manager = ProcessQueueManager()
