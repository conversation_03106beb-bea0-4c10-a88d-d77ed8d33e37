# Task Assignment System

A Flask-based web application for assigning tasks to employees with automated notification system.

## Features

### 📋 Task Management
- Create tasks with detailed descriptions
- Set deadlines for tasks
- Assign tasks to one or multiple employees
- Mark tasks as completed
- Delete tasks

### 👥 Employee Management
- Add new employees with name, email, and phone
- **Malaysian Phone Number Formatting**: Automatic formatting and validation
  - Input: `0123456789` or `123456789` → Display: `(+60)12-345 6789`
  - Input: `01156229909` or `1156229909` → Display: `(+60)11-5622 9909`
  - Supports area codes: 10, 11, 12, 13, 14, 15, 16, 17, 18, 19
  - Special handling for area code 11 (requires 10 digits total)
- Edit existing employee information
- Delete employees
- View all employees in a table format

### 🔔 WhatsApp Notification System
- **📱 WhatsApp Integration**: Send notifications via WhatsApp using WAHA (WhatsApp HTTP API)
- **Task Assignment Notifications**: Immediate WhatsApp message when task is assigned
- **Daily Reminders**: Send daily WhatsApp reminders until deadline
- **Weekly Reminders**: Send weekly WhatsApp reminders until deadline
- **Every N Days**: Send WhatsApp notifications every specified number of days
- **Days Before Deadline**: Send WhatsApp notification N days before the deadline
- **Rich Messages**: Formatted messages with emojis and task details
- **Malaysian Phone Support**: Automatic conversion to WhatsApp format

### 📊 Dashboard
- Main dashboard showing all tasks in table format
- Display employee names, emails, phone numbers
- Show task descriptions and deadlines
- Task status (pending/completed)
- Quick actions to complete or delete tasks

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. **Set up WAHA (WhatsApp HTTP API)**:
```bash
# Run WAHA with Docker
docker run -it --rm -p 3000:3000/tcp devlikeapro/waha

# Open WAHA dashboard
# Go to: http://localhost:3000/dashboard
# Create session named 'default'
# Scan QR code with WhatsApp
```

3. Run the application:
```bash
python app.py
```

4. Open your browser and navigate to:
```
http://127.0.0.1:5004
```

5. **Test WhatsApp integration**:
```bash
python test_whatsapp.py
```

## Usage

### Getting Started
1. **Add Employees**: Go to "Employees" menu and add your team members
   - Enter phone numbers in any format: `0123456789`, `123456789`, or `+60123456789`
   - System automatically formats to Malaysian standard: `(+60)12-345 6789`
2. **Create Tasks**: Use "Create Task" to assign work with deadlines
3. **Monitor Progress**: Use the dashboard to track task status
4. **Manage Notifications**: Configure notification schedules when creating tasks

### Phone Number Format Examples
- **Input**: `123449807` → **Output**: `(+60)12-344 9807`
- **Input**: `1156229909` → **Output**: `(+60)11-5622 9909`
- **Input**: `0177604822` → **Output**: `(+60)17-760 4822`
- **Input**: `0123456789` → **Output**: `(+60)12-345 6789`

### WhatsApp Notification Types
- **Daily**: Sends WhatsApp reminders every day at 9 AM until deadline
- **Weekly**: Sends WhatsApp reminders every Monday at 9 AM until deadline
- **Every N Days**: Sends WhatsApp reminders every specified number of days
- **N Days Before**: Sends a single WhatsApp reminder N days before the deadline

### WhatsApp Message Examples
**Task Assignment:**
```
📋 *New Task Assigned*

Hello John Doe! 👋

You have been assigned a new task:

*Task:* Complete quarterly report
*Deadline:* 15/08/2025 at 05:00 PM

Please complete this task before the deadline.

Thank you! 🙏
```

**Deadline Reminder:**
```
⚠️ *Task Deadline Reminder*

Hello John Doe! 👋

*Task:* Complete quarterly report
*Deadline:* 15/08/2025 at 05:00 PM
*Days Left:* 2 day(s)

Please ensure you complete this task on time. ⏰

Thank you! 🙏
```

## Database

The application uses SQLite database (`task_assignment.db`) with the following tables:
- **Employee**: Stores employee information
- **Task**: Stores task details and notification settings
- **TaskAssignment**: Links tasks to employees

## File Structure

```
task_assign/
├── app.py              # Main Flask application
├── requirements.txt    # Python dependencies
├── test_app.py        # Test script
├── README.md          # This file
├── templates/         # HTML templates
│   ├── base.html      # Base template with navigation
│   ├── index.html     # Main dashboard
│   ├── employees.html # Employee list
│   ├── add_employee.html
│   ├── edit_employee.html
│   └── create_task.html
└── static/           # Static files (CSS, JS, images)
```

## Technical Details

- **Framework**: Flask 2.3.3
- **Database**: SQLAlchemy with SQLite
- **Scheduler**: APScheduler for background notifications
- **Frontend**: Bootstrap 5.1.3 with Font Awesome icons
- **Port**: 5004 (configurable)

## Customization

### Email/SMS Notifications
The current notification system prints to console. To implement actual email/SMS:

1. Modify the `send_notification()` function in `app.py`
2. Add email service (SMTP) or SMS service integration
3. Configure credentials in environment variables

### Styling
- Modify templates in the `templates/` folder
- Add custom CSS in the `static/` folder
- Update Bootstrap classes as needed

## Testing

Run the test script to verify functionality:
```bash
python test_app.py
```

## Security Notes

- Change the `SECRET_KEY` in production
- Use environment variables for sensitive configuration
- Implement user authentication for production use
- Use HTTPS in production environment

## Support

For issues or questions, please check the code comments or modify the application as needed for your specific requirements.
