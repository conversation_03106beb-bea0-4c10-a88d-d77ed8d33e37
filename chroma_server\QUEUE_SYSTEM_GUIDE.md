# Process Queue Management System

## Overview

The Process Queue Management System limits concurrent n8n workflows to a maximum of 3 processes, with automatic queuing and processing of pending uploads. This ensures optimal resource usage and prevents system overload.

## Features

### ✅ Concurrent Process Limiting
- Maximum 3 sources can be processed by n8n simultaneously
- Applies to both PDF uploads and YouTube transcript uploads
- When limit is reached, new uploads are automatically queued

### ✅ Intelligent Queue Management
- **Uploads 1-3**: Process immediately through n8n
- **Upload 4+**: Queue the source, extract/process text locally but don't send to n8n
- **Process completion**: When any of the 3 active processes completes, automatically start the next queued item

### ✅ Always Accept Uploads
- Never reject uploads due to queue limits
- Always extract content immediately (PDF extraction, YouTube transcript processing)
- Provide immediate feedback about queue status

### ✅ Real-time Status Tracking
- Track which processes are active vs queued
- Monitor chunk processing progress
- Automatic queue advancement when slots become available

## API Endpoints

### Queue Status
```http
GET /api/queue-status
```

**Response:**
```json
{
  "status": "ok",
  "queue": {
    "active_processes": [
      {
        "session_id": "abc123",
        "filename": "document.pdf",
        "type": "pdf",
        "started_at": "2025-08-20T15:30:00",
        "total_chunks": 5,
        "completed_chunks": 2,
        "progress": 40.0
      }
    ],
    "pending_queue": [
      {
        "session_id": "def456",
        "filename": "video.yt",
        "type": "youtube",
        "position": 1,
        "created_at": "2025-08-20T15:32:00"
      }
    ],
    "active_count": 1,
    "pending_count": 1,
    "max_concurrent": 3,
    "available_slots": 2
  },
  "sessions": {
    "total_sessions": 10,
    "active_sessions": 1,
    "queued_sessions": 1,
    "completed_sessions": 8
  }
}
```

### Session Status
```http
GET /api/session-status/{session_id}
```

**Response:**
```json
{
  "status": "ok",
  "session": {
    "session_id": "abc123",
    "filename": "document.pdf",
    "type": "pdf",
    "status": "active",
    "started_at": "2025-08-20T15:30:00",
    "total_chunks": 5,
    "completed_chunks": 2,
    "progress": 40.0
  }
}
```

### Queue Notifications
```http
GET /api/queue-notifications
```

**Response:**
```json
{
  "status": "ok",
  "notifications": [
    {
      "endpoint_type": "pdf",
      "status": "queued",
      "filename": "document.pdf",
      "session_id": "abc123",
      "message": "Queued at position 2",
      "queue_position": 2,
      "timestamp": "2025-08-20T15:32:00"
    }
  ]
}
```

## Upload Response Changes

### PDF Upload Response
```json
{
  "status": "success",
  "message": "PDF processed successfully and processing started",
  "data": {
    "filename": "document.pdf",
    "estimated_tokens": 1250,
    "chunk_count": 3,
    "workflow_triggered": true,
    "processing_method": "pdf_extraction",
    "queue_status": "started",
    "session_id": "abc123"
  }
}
```

### YouTube Transcript Upload Response
```json
{
  "status": "success",
  "message": "YouTube transcript processed successfully and queued at position 2",
  "data": {
    "video_title": "AI Tutorial",
    "video_url": "https://youtube.com/watch?v=example",
    "source": "youtube_transcripts",
    "transcript_length": 15420,
    "estimated_tokens": 3855,
    "chunk_count": 4,
    "workflow_triggered": false,
    "processing_method": "youtube_transcript",
    "queue_status": "queued",
    "session_id": "def456"
  }
}
```

## Queue Status Values

- **`started`**: Process began immediately (slots available)
- **`queued`**: Process added to queue (all slots occupied)
- **`active`**: Process currently running in n8n
- **`completed`**: Process finished successfully
- **`failed`**: Process encountered an error

## Testing the Queue System

Run the test script to verify the queue system is working correctly:

```bash
python test_queue_system.py
```

The test script will:
1. ✅ Test queue status endpoints
2. ✅ Upload multiple files to test concurrent limits
3. ✅ Verify queue progression
4. ✅ Check API endpoint functionality

## Frontend Integration

### Displaying Queue Status
```javascript
async function getQueueStatus() {
    const response = await fetch('/api/queue-status');
    const data = await response.json();
    
    // Show active processes
    data.queue.active_processes.forEach(process => {
        console.log(`${process.filename}: ${process.progress}% complete`);
    });
    
    // Show pending queue
    data.queue.pending_queue.forEach(process => {
        console.log(`${process.filename}: Position ${process.position} in queue`);
    });
}
```

### Monitoring Session Progress
```javascript
async function monitorSession(sessionId) {
    const response = await fetch(`/api/session-status/${sessionId}`);
    const data = await response.json();
    
    if (data.session.status === 'active') {
        const progress = data.session.progress || 0;
        console.log(`Processing: ${progress}%`);
    } else if (data.session.status === 'queued') {
        console.log(`Queued at position ${data.session.queue_position}`);
    }
}
```

## Configuration

The queue system can be configured by modifying the `ProcessQueueManager` initialization:

```python
# In src/flows/process_queue.py
queue_manager = ProcessQueueManager(max_concurrent_processes=3)
```

To change the maximum concurrent processes, update the value and restart the server.

## Troubleshooting

### Queue Not Processing
1. Check if n8n is running and accessible
2. Verify webhook URLs are correct
3. Check server logs for errors

### Processes Stuck in Queue
1. Check active processes: `GET /api/queue-status`
2. Look for failed processes that didn't complete properly
3. Restart the server if needed (queue state will be rebuilt)

### High Memory Usage
1. Monitor the number of completed sessions
2. Old sessions are automatically cleaned up after 24 hours
3. Manually trigger cleanup if needed

## Implementation Details

### Core Components
- **ProcessQueueManager**: Manages queue and concurrent process limits
- **StateManager**: Tracks session metadata and notifications
- **Enhanced Upload Endpoints**: Always accept uploads, use queue system
- **Updated Callbacks**: Detect completion and trigger queue advancement

### Thread Safety
All queue operations are thread-safe using locks to prevent race conditions during concurrent access.

### Automatic Cleanup
Old completed/failed sessions are automatically cleaned up every hour to prevent memory leaks.
